<template>
  <div class="staff-view-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">员工信息管理</h1>
            <p class="text-sm text-gray-600 mt-1">管理员工基本信息、资质证书、培训记录和考核档案</p>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="flex gap-3">
          <el-button
            type="default"
            @click="handleDepartmentManagement"
            class="border-gray-300 hover:border-pink-500 hover:text-pink-500"
          >
            <el-icon class="mr-2">
              <Setting />
            </el-icon>
            部门管理
          </el-button>
          <el-button
            type="primary"
            @click="handleAddStaff"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            <el-icon class="mr-2">
              <Plus />
            </el-icon>
            新增员工
          </el-button>
        </div>
      </div>
    </div>

    <!-- 搜索过滤区域 -->
    <FilterPanel
      :fields="filterFields"
      :filters="currentFilters"
      @search="handleSearch"
      class="mb-6"
    />

    <!-- 数据表格 -->
    <StaffTable
      ref="staffTableRef"
      :filters="currentFilters"
      @edit="handleEditStaff"
      @row-click="handleRowClick"
      @manage-certificate="handleManageCertificate"
      @manage-training="handleManageTraining"
      @manage-assessment="handleManageAssessment"
      @manage-health="handleManageHealth"
      @delete="handleDeleteStaff"
    />

    <!-- 员工表单弹窗 -->
    <StaffFormDialog
      v-model:visible="formVisible"
      :item-id="currentStaff?.sid"
      :mode="formMode"
      @save="handleSaveStaff"
    />

    <!-- 员工详情弹窗 -->
    <StaffDetailDialog
      v-model:visible="detailVisible"
      :item-id="currentStaff?.sid"
      @edit="handleEditFromDetail"
    />

    <!-- 资质证书管理弹窗 -->
    <StaffCertificateDialog
      v-model:visible="certificateVisible"
      :staff-data="currentStaff"
      @save="handleSaveCertificate"
    />

    <!-- 培训记录管理弹窗 -->
    <StaffTrainingDialog
      v-model:visible="trainingVisible"
      :staff-data="currentStaff"
      @save="handleSaveTraining"
    />

    <!-- 考核记录管理弹窗 -->
    <StaffAssessmentDialog
      v-model:visible="assessmentVisible"
      :staff-data="currentStaff"
      @save="handleSaveAssessment"
    />

    <!-- 健康检查记录弹窗 -->
    <StaffHealthDialog
      v-model:visible="healthVisible"
      :staff-data="currentStaff"
      @save="handleSaveHealth"
    />

    <!-- 部门管理弹窗 -->
    <DepartmentManagementDialog
      v-model:visible="departmentVisible"
      @save="handleSaveDepartment"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElButton, ElMessage, ElIcon } from 'element-plus'
import { Plus, Setting } from '@element-plus/icons-vue'
import FilterPanel from '@/components/FilterPanel.vue'
import StaffTable from './components/StaffTable.vue'
import StaffFormDialog from './components/StaffFormDialog.vue'
import StaffDetailDialog from './components/StaffDetailDialog.vue'
import StaffCertificateDialog from './components/StaffCertificateDialog.vue'
import StaffTrainingDialog from './components/StaffTrainingDialog.vue'
import StaffAssessmentDialog from './components/StaffAssessmentDialog.vue'
import StaffHealthDialog from './components/StaffHealthDialog.vue'
import DepartmentManagementDialog from './components/DepartmentManagementDialog.vue'
import { useBaseDataStore } from '@/stores/baseData.js'
import { computed } from 'vue'

// 使用基础数据store
const baseDataStore = useBaseDataStore()

// 响应式数据
const formVisible = ref(false)
const detailVisible = ref(false)
const certificateVisible = ref(false)
const trainingVisible = ref(false)
const assessmentVisible = ref(false)
const healthVisible = ref(false)
const departmentVisible = ref(false)
const formMode = ref('add') // 'add' | 'edit'
const currentStaff = ref(null)

// 获取 table 组件引用
const staffTableRef = ref(null)

// 当前过滤条件
const currentFilters = reactive({
  sk: '',
  department: '',
})

// 过滤器字段配置
const filterFields = computed(() => [
  {
    key: 'department',
    type: 'select',
    label: '所属部门',
    placeholder: '请选择部门',
    options: baseDataStore.departments.getOptions(),
  },
  {
    key: 'sk',
    type: 'input',
    label: '关键字',
    placeholder: '请输入姓名或员工号',
  },
])

// 部门管理
const handleDepartmentManagement = () => {
  departmentVisible.value = true
}

// 新增员工
const handleAddStaff = () => {
  currentStaff.value = null
  formMode.value = 'add'
  formVisible.value = true
}

// 编辑员工
const handleEditStaff = (staff) => {
  currentStaff.value = staff
  formMode.value = 'edit'
  formVisible.value = true
}

// 从详情弹窗编辑员工
const handleEditFromDetail = (staff) => {
  currentStaff.value = staff
  formMode.value = 'edit'
  detailVisible.value = false
  formVisible.value = true
}

// 表单提交成功
const handleSaveStaff = () => {
  formVisible.value = false
  currentStaff.value = null
  staffTableRef.value?.refresh() // 刷新表格数据
}

// 搜索 - 点击搜索按钮时触发
const handleSearch = () => {
  // 重置到第一页并重新加载数据
  staffTableRef.value?.resetPagination()
}

// 行点击 - 查看详情
const handleRowClick = (staff) => {
  currentStaff.value = staff
  detailVisible.value = true
}

// 管理证书
const handleManageCertificate = (staff) => {
  currentStaff.value = staff
  certificateVisible.value = true
}

// 管理培训
const handleManageTraining = (staff) => {
  currentStaff.value = staff
  trainingVisible.value = true
}

// 管理考核
const handleManageAssessment = (staff) => {
  currentStaff.value = staff
  assessmentVisible.value = true
}

// 管理健康
const handleManageHealth = (staff) => {
  currentStaff.value = staff
  healthVisible.value = true
}

// 删除员工 - 直接在表格组件中处理
const handleDeleteStaff = () => {
  // 这个方法由表格组件处理，这里只是为了保持接口一致性
}

const handleSaveCertificate = () => {
  ElMessage.success('证书信息保存成功')
  certificateVisible.value = false
  staffTableRef.value?.refresh()
}

const handleSaveTraining = () => {
  ElMessage.success('培训记录保存成功')
  trainingVisible.value = false
  staffTableRef.value?.refresh()
}

const handleSaveAssessment = () => {
  ElMessage.success('考核记录保存成功')
  assessmentVisible.value = false
  staffTableRef.value?.refresh()
}

const handleSaveHealth = () => {
  ElMessage.success('健康检查记录保存成功')
  healthVisible.value = false
  staffTableRef.value?.refresh()
}

const handleSaveDepartment = () => {
  ElMessage.success('部门信息保存成功')
  departmentVisible.value = false
  // 刷新基础数据store中的部门数据
  baseDataStore.departments.refresh()
}
</script>

<style scoped>
.staff-view-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}
</style>
